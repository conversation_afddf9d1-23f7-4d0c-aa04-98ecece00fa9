# Windows Server Process Shutdown Fix

## Problem Analysis

The ApiServerManager was not properly shutting down the server process when the main application closed on Windows due to several issues:

1. **AppLifecycleListener Reliability**: The `AppLifecycleListener.onExitRequested` callback may not always be triggered reliably on Windows, especially when the application is forcefully closed.

2. **Incomplete Process Termination**: The Windows-specific process termination logic didn't wait for the process to actually terminate after calling `taskkill`.

3. **No Orphaned Process Cleanup**: There was no mechanism to clean up server processes that remained running after the main application crashed or was killed unexpectedly.

4. **Missing Process Monitoring**: The singleton had no way to detect when the parent application was no longer running.

## Solution Implemented

### 1. Enhanced Process Termination (`_terminateManagedProcess`)

- **Graceful Termination First**: Attempts graceful termination with a 2-second timeout
- **Force Kill with Verification**: Uses `taskkill /F /PID` if graceful termination fails
- **Process Tracking File Cleanup**: Removes tracking files after successful termination
- **Proper Waiting**: Waits for the force kill to take effect before proceeding

### 2. Watchdog Timer System

- **Process Monitoring**: Periodically checks if the main application process is still running
- **Automatic Cleanup**: Performs emergency shutdown if the parent process is no longer detected
- **Windows-Specific**: Only activated on Windows platform where the issue is most prevalent

### 3. Process Tracking Files

- **Creation**: Creates `.pid` files in the system temp directory when starting server processes
- **Cleanup on Startup**: Scans for and cleans up orphaned processes from previous runs
- **Emergency Cleanup**: Removes tracking files during emergency shutdowns

### 4. Emergency Shutdown Mechanism

- **Independent Operation**: Works without relying on Flutter's lifecycle management
- **Synchronous Cleanup**: Uses synchronous operations to ensure completion
- **Comprehensive**: Stops all timers and forcefully terminates processes

## Key Changes Made

### ApiServerManager Class Enhancements

1. **Added Watchdog Timer**:
   ```dart
   Timer? _watchdogTimer;
   static const Duration _watchdogInterval = Duration(seconds: 10);
   ```

2. **Enhanced Constructor**:
   ```dart
   ApiServerManager._internal() {
     if (Platform.isWindows) {
       _startWatchdogTimer();
     }
   }
   ```

3. **Improved Process Termination**:
   - Added proper waiting for graceful termination
   - Enhanced force kill with verification
   - Added process tracking file cleanup

4. **Process Tracking System**:
   - `_createProcessTrackingFile()` - Creates tracking files
   - `_removeProcessTrackingFile()` - Removes tracking files
   - `cleanupOrphanedProcesses()` - Static method for startup cleanup

5. **Watchdog Monitoring**:
   - `_startWatchdogTimer()` - Monitors parent process
   - `_isMainApplicationRunning()` - Checks if main app is alive
   - `_performEmergencyShutdown()` - Emergency cleanup

### Initialization Changes

- Added orphaned process cleanup to `initApiServer()`
- Ensures clean startup by removing leftover processes from previous runs

## Testing the Fix

### 1. Normal Application Exit
1. Start the application
2. Verify the server process is running (check Task Manager)
3. Close the application normally
4. Verify the server process is terminated within a few seconds

### 2. Force Kill Testing
1. Start the application
2. Note the server process PID
3. Force kill the main application process using Task Manager
4. Verify the server process is automatically terminated within 10-20 seconds

### 3. Orphaned Process Cleanup
1. Manually start a server process that would be orphaned
2. Create a tracking file in the temp directory
3. Restart the application
4. Verify the orphaned process is cleaned up during initialization

### 4. Crash Recovery
1. Simulate an application crash
2. Restart the application
3. Verify no orphaned server processes remain

## Verification Commands

### Check for Running Server Processes
```cmd
tasklist | findstr server.exe
```

### Check for Tracking Files
```cmd
dir %TEMP%\anki_guru_server_*.pid
```

### Monitor Process Termination
```cmd
wmic process where "name='server.exe'" get ProcessId,ParentProcessId,CommandLine
```

## Benefits

1. **Reliable Cleanup**: Server processes are now properly terminated even if the main application crashes
2. **Automatic Recovery**: Orphaned processes from previous runs are automatically cleaned up
3. **Resource Management**: Prevents accumulation of zombie server processes
4. **Windows Optimization**: Specifically addresses Windows process management challenges
5. **Backward Compatibility**: Maintains existing functionality while adding robustness

## Monitoring and Logging

The solution includes comprehensive logging to help diagnose any remaining issues:

- Process startup and termination events
- Watchdog timer activities
- Emergency shutdown operations
- Orphaned process cleanup activities
- Tracking file management

Check the application logs for entries containing:
- "Terminating managed server process"
- "Emergency shutdown"
- "Cleaning up orphaned server process"
- "Watchdog timer"
